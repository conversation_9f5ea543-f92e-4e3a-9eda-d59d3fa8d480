<?php

namespace App\Service;

use App\Repository\DocumentRepository;
use Doctrine\ORM\EntityManagerInterface;

class AdvancedPredictionService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private DocumentRepository $documentRepository
    ) {}

    /**
     * Prédiction avancée du temps de traitement basée sur plusieurs facteurs
     */
    public function predictAdvancedProcessingTime($document): array
    {
        $docType = $document->getDocType();
        $currentSteps = $document->getCurrentSteps();

        // 1. Prédiction basée sur le type de document
        $docTypePrediction = $this->predictByDocumentType($docType);

        // 2. Prédiction basée sur les états actuels
        $statePrediction = $this->predictByCurrentStates($currentSteps);

        // 3. Prédiction basée sur les tendances saisonnières
        $seasonalPrediction = $this->predictBySeason();

        // 4. Prédiction basée sur la charge de travail actuelle
        $workloadPrediction = $this->predictByWorkload();

        // 5. Combiner les prédictions avec des poids
        $finalPrediction = $this->combinePredictions([
            'doc_type' => ['value' => $docTypePrediction, 'weight' => 0.4],
            'states' => ['value' => $statePrediction, 'weight' => 0.3],
            'seasonal' => ['value' => $seasonalPrediction, 'weight' => 0.2],
            'workload' => ['value' => $workloadPrediction, 'weight' => 0.1]
        ]);

        return [
            'predicted_days' => round($finalPrediction, 1),
            'confidence_level' => $this->calculateConfidence($docTypePrediction, $statePrediction),
            'breakdown' => [
                'doc_type_factor' => round($docTypePrediction, 1),
                'state_factor' => round($statePrediction, 1),
                'seasonal_factor' => round($seasonalPrediction, 1),
                'workload_factor' => round($workloadPrediction, 1)
            ],
            'recommendations' => $this->generateRecommendations($finalPrediction, $currentSteps)
        ];
    }

    /**
     * Prédiction basée sur le type de document avec données historiques
     */
    private function predictByDocumentType(string $docType): float
    {
        $conn = $this->entityManager->getConnection();

        $sql = "
            SELECT
                AVG(total_days) as avg_days,
                COUNT(*) as sample_size
            FROM (
                SELECT
                    d.id,
                    DATEDIFF(
                        GREATEST(
                            COALESCE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE[0].exit')), '1900-01-01'),
                            COALESCE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.Quality[0].exit')), '1900-01-01'),
                            COALESCE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit')), '1900-01-01')
                        ),
                        LEAST(
                            COALESCE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter')), '2099-01-01'),
                            COALESCE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE[0].enter')), '2099-01-01')
                        )
                    ) as total_days
                FROM document d
                WHERE d.doc_type = ?
                AND d.state_timestamps IS NOT NULL
                AND d.state_timestamps != '{}'
                HAVING total_days > 0 AND total_days < 365
            ) as completed_docs
        ";

        $result = $conn->executeQuery($sql, [$docType])->fetchAssociative();

        return $result['avg_days'] && $result['sample_size'] > 5
            ? (float)$result['avg_days']
            : $this->getDefaultTimeByDocType($docType);
    }

    /**
     * Prédiction basée sur les états actuels du document
     */
    private function predictByCurrentStates(array $currentSteps): float
    {
        if (empty($currentSteps)) {
            return 15.0; // Valeur par défaut
        }

        $stateAverages = $this->documentRepository->getAverageTimeByState();
        $remainingTime = 0;
        $stateCount = 0;

        foreach (array_keys($currentSteps) as $state) {
            if (isset($stateAverages[$state])) {
                $remainingTime += $stateAverages[$state];
                $stateCount++;
            }
        }

        return $stateCount > 0 ? $remainingTime / $stateCount : 15.0;
    }

    /**
     * Prédiction basée sur les tendances saisonnières
     */
    private function predictBySeason(): float
    {
        $currentMonth = (int)date('n');

        // Facteurs saisonniers basés sur l'expérience (ajustables)
        $seasonalFactors = [
            1 => 1.2,  // Janvier - retour de vacances
            2 => 1.0,  // Février
            3 => 0.9,  // Mars
            4 => 0.8,  // Avril
            5 => 0.9,  // Mai
            6 => 1.0,  // Juin
            7 => 1.3,  // Juillet - vacances
            8 => 1.4,  // Août - vacances
            9 => 0.8,  // Septembre - reprise
            10 => 0.9, // Octobre
            11 => 1.0, // Novembre
            12 => 1.2  // Décembre - fin d'année
        ];

        $baseLine = 12.0; // Temps de base
        return $baseLine * ($seasonalFactors[$currentMonth] ?? 1.0);
    }

    /**
     * Prédiction basée sur la charge de travail actuelle
     */
    private function predictByWorkload(): float
    {
        $conn = $this->entityManager->getConnection();

        // Compter les documents actifs par état
        $sql = "
            SELECT
                COUNT(*) as active_docs
            FROM document d
            WHERE d.current_steps IS NOT NULL
            AND d.current_steps != '{}'
            AND NOT EXISTS (
                SELECT 1 FROM visa v
                WHERE v.released_drawing_id = d.id
                AND v.name = 'visa_Costing'
                AND v.status = 'valid'
            )
        ";

        $activeDocs = $conn->executeQuery($sql)->fetchOne();

        // Facteur de charge : plus il y a de documents actifs, plus les délais augmentent
        $workloadFactor = 1.0 + ($activeDocs / 1000); // Ajustable selon votre contexte

        return 10.0 * $workloadFactor;
    }

    /**
     * Combine plusieurs prédictions avec des poids
     */
    private function combinePredictions(array $predictions): float
    {
        $weightedSum = 0;
        $totalWeight = 0;

        foreach ($predictions as $prediction) {
            $weightedSum += $prediction['value'] * $prediction['weight'];
            $totalWeight += $prediction['weight'];
        }

        return $totalWeight > 0 ? $weightedSum / $totalWeight : 15.0;
    }

    /**
     * Calcule le niveau de confiance de la prédiction
     */
    private function calculateConfidence(float $docTypePrediction, float $statePrediction): string
    {
        $variance = abs($docTypePrediction - $statePrediction);

        if ($variance < 2) return 'Élevé';
        if ($variance < 5) return 'Moyen';
        return 'Faible';
    }

    /**
     * Génère des recommandations basées sur la prédiction
     */
    private function generateRecommendations(float $predictedDays, array $currentSteps): array
    {
        $recommendations = [];

        if ($predictedDays > 20) {
            $recommendations[] = "Délai prévu élevé - Prioriser ce document";
            $recommendations[] = "Vérifier les goulots d'étranglement dans les états actuels";
        }

        if ($predictedDays < 5) {
            $recommendations[] = "Traitement rapide prévu - Document prioritaire";
        }

        // Recommandations spécifiques par état
        foreach (array_keys($currentSteps) as $state) {
            if ($state === 'Quality') {
                $recommendations[] = "État Quality détecté - Prévoir contrôles qualité";
            }
            if ($state === 'Achat_F30') {
                $recommendations[] = "État Achat détecté - Vérifier disponibilité fournisseurs";
            }
        }

        return array_unique($recommendations);
    }

    /**
     * Valeurs par défaut par type de document
     */
    private function getDefaultTimeByDocType(string $docType): float
    {
        return match($docType) {
            'ASSY' => 18.0,
            'MACH' => 15.0,
            'MOLD' => 22.0,
            'DOC' => 8.0,
            'PUR' => 12.0,
            default => 15.0
        };
    }

    /**
     * Analyse des tendances pour améliorer les prédictions futures
     */
    public function analyzePredictionAccuracy(): array
    {
        // Cette méthode pourrait comparer les prédictions passées avec la réalité
        // pour améliorer continuellement l'algorithme

        return [
            'accuracy_rate' => 75.0, // À calculer réellement
            'avg_error' => 2.3,      // Écart moyen en jours
            'improvement_suggestions' => [
                'Affiner les facteurs saisonniers',
                'Améliorer la prédiction par type de document',
                'Intégrer plus de variables contextuelles'
            ]
        ];
    }

    /**
     * Prédiction de la charge de travail future
     */
    public function predictWorkloadTrends(int $daysAhead = 30): array
    {
        $conn = $this->entityManager->getConnection();

        // Analyser les tendances d'entrée de nouveaux documents
        $sql = "
            SELECT
                DATE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter'))) as date,
                COUNT(*) as new_docs
            FROM document d
            WHERE JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter') IS NOT NULL
            AND JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter')) >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter')))
            ORDER BY date DESC
        ";

        $recentTrends = $conn->executeQuery($sql)->fetchAllAssociative();

        // Calculer la moyenne mobile
        $avgNewDocs = count($recentTrends) > 0
            ? array_sum(array_column($recentTrends, 'new_docs')) / count($recentTrends)
            : 10;

        // Prédire la charge future
        $predictions = [];
        for ($i = 1; $i <= $daysAhead; $i++) {
            $date = date('Y-m-d', strtotime("+{$i} days"));
            $predictions[] = [
                'date' => $date,
                'predicted_new_docs' => round($avgNewDocs * $this->getSeasonalFactor($date)),
                'predicted_workload' => 'normal' // À affiner
            ];
        }

        return $predictions;
    }

    /**
     * Facteur saisonnier pour une date donnée
     */
    private function getSeasonalFactor(string $date): float
    {
        $month = (int)date('n', strtotime($date));
        $factors = [1 => 1.2, 2 => 1.0, 3 => 0.9, 4 => 0.8, 5 => 0.9, 6 => 1.0,
                   7 => 1.3, 8 => 1.4, 9 => 0.8, 10 => 0.9, 11 => 1.0, 12 => 1.2];
        return $factors[$month] ?? 1.0;
    }
}
