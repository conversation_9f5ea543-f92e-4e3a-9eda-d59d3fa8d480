<?php

namespace App\Service;

use App\Repository\DocumentRepository;
use Doctrine\ORM\EntityManagerInterface;

class AdvancedMetricsService
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private DocumentRepository $documentRepository
    ) {}

    /**
     * Calcule des métriques de performance avancées
     */
    public function calculateAdvancedMetrics(): array
    {
        return [
            'throughput' => $this->calculateThroughput(),
            'cycle_time' => $this->calculateCycleTime(),
            'lead_time' => $this->calculateLeadTime(),
            'efficiency' => $this->calculateEfficiency(),
            'quality_metrics' => $this->calculateQualityMetrics(),
            'predictability' => $this->calculatePredictability(),
            'resource_utilization' => $this->calculateResourceUtilization()
        ];
    }

    /**
     * Calcule le débit (documents traités par période)
     */
    private function calculateThroughput(): array
    {
        $conn = $this->entityManager->getConnection();
        
        $sql = "
            SELECT 
                DATE_FORMAT(v.date_visa, '%Y-%m') as month,
                COUNT(*) as completed_documents
            FROM visa v
            WHERE v.name = 'visa_Costing' 
            AND v.status = 'valid'
            AND v.date_visa >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(v.date_visa, '%Y-%m')
            ORDER BY month DESC
        ";
        
        $result = $conn->executeQuery($sql)->fetchAllAssociative();
        
        $monthlyThroughput = array_column($result, 'completed_documents');
        $avgThroughput = count($monthlyThroughput) > 0 ? array_sum($monthlyThroughput) / count($monthlyThroughput) : 0;
        
        return [
            'monthly_data' => $result,
            'average_per_month' => round($avgThroughput, 1),
            'trend' => $this->calculateSimpleTrend($monthlyThroughput)
        ];
    }

    /**
     * Calcule le temps de cycle (temps de traitement actif)
     */
    private function calculateCycleTime(): array
    {
        $stateAverages = $this->documentRepository->getAverageTimeByState();
        
        $activeCycleTime = array_sum($stateAverages);
        $stateCount = count($stateAverages);
        
        return [
            'total_active_time' => round($activeCycleTime, 1),
            'average_per_state' => $stateCount > 0 ? round($activeCycleTime / $stateCount, 1) : 0,
            'state_breakdown' => $stateAverages,
            'efficiency_rating' => $this->rateEfficiency($activeCycleTime)
        ];
    }

    /**
     * Calcule le lead time (temps total du processus)
     */
    private function calculateLeadTime(): array
    {
        $conn = $this->entityManager->getConnection();
        
        $sql = "
            SELECT 
                AVG(DATEDIFF(
                    COALESCE(
                        JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit')),
                        JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.Quality[0].exit')),
                        NOW()
                    ),
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter'))
                )) as avg_lead_time,
                MIN(DATEDIFF(
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit')),
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter'))
                )) as min_lead_time,
                MAX(DATEDIFF(
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit')),
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter'))
                )) as max_lead_time
            FROM document d
            WHERE JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter') IS NOT NULL
            AND JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit') IS NOT NULL
        ";
        
        $result = $conn->executeQuery($sql)->fetchAssociative();
        
        return [
            'average' => round($result['avg_lead_time'] ?? 0, 1),
            'minimum' => $result['min_lead_time'] ?? 0,
            'maximum' => $result['max_lead_time'] ?? 0,
            'target' => 20, // Cible à définir selon vos objectifs
            'performance' => $this->calculatePerformanceVsTarget($result['avg_lead_time'] ?? 0, 20)
        ];
    }

    /**
     * Calcule l'efficacité globale du processus
     */
    private function calculateEfficiency(): array
    {
        $cycleTime = $this->calculateCycleTime();
        $leadTime = $this->calculateLeadTime();
        
        $efficiency = $leadTime['average'] > 0 
            ? ($cycleTime['total_active_time'] / $leadTime['average']) * 100 
            : 0;
        
        return [
            'percentage' => round($efficiency, 1),
            'rating' => $this->rateEfficiency($efficiency),
            'waste_time' => round($leadTime['average'] - $cycleTime['total_active_time'], 1),
            'improvement_potential' => round(100 - $efficiency, 1)
        ];
    }

    /**
     * Calcule les métriques de qualité
     */
    private function calculateQualityMetrics(): array
    {
        $conn = $this->entityManager->getConnection();
        
        // Taux de retour (documents qui reviennent en arrière)
        $sql = "
            SELECT 
                COUNT(DISTINCT d.id) as total_docs,
                COUNT(DISTINCT CASE 
                    WHEN JSON_EXTRACT(d.state_timestamps, '$.Quality') IS NOT NULL 
                    AND JSON_LENGTH(JSON_EXTRACT(d.state_timestamps, '$.Quality')) > 1 
                    THEN d.id 
                END) as returned_docs
            FROM document d
            WHERE d.state_timestamps IS NOT NULL
        ";
        
        $result = $conn->executeQuery($sql)->fetchAssociative();
        
        $returnRate = $result['total_docs'] > 0 
            ? ($result['returned_docs'] / $result['total_docs']) * 100 
            : 0;
        
        return [
            'return_rate' => round($returnRate, 1),
            'first_pass_yield' => round(100 - $returnRate, 1),
            'quality_rating' => $returnRate < 5 ? 'Excellent' : ($returnRate < 15 ? 'Bon' : 'À améliorer')
        ];
    }

    /**
     * Calcule la prévisibilité du processus
     */
    private function calculatePredictability(): array
    {
        $conn = $this->entityManager->getConnection();
        
        $sql = "
            SELECT 
                STDDEV(DATEDIFF(
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit')),
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter'))
                )) as std_deviation,
                AVG(DATEDIFF(
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit')),
                    JSON_UNQUOTE(JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter'))
                )) as avg_time
            FROM document d
            WHERE JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter') IS NOT NULL
            AND JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit') IS NOT NULL
        ";
        
        $result = $conn->executeQuery($sql)->fetchAssociative();
        
        $coefficientVariation = $result['avg_time'] > 0 
            ? ($result['std_deviation'] / $result['avg_time']) * 100 
            : 0;
        
        return [
            'coefficient_of_variation' => round($coefficientVariation, 1),
            'predictability_rating' => $coefficientVariation < 20 ? 'Élevée' : ($coefficientVariation < 40 ? 'Moyenne' : 'Faible'),
            'standard_deviation' => round($result['std_deviation'] ?? 0, 1)
        ];
    }

    /**
     * Calcule l'utilisation des ressources
     */
    private function calculateResourceUtilization(): array
    {
        $conn = $this->entityManager->getConnection();
        
        // Analyser la charge par état
        $sql = "
            SELECT 
                JSON_UNQUOTE(JSON_EXTRACT(d.current_steps, '$.*')) as current_state,
                COUNT(*) as documents_count
            FROM document d
            WHERE d.current_steps IS NOT NULL 
            AND d.current_steps != '{}'
            GROUP BY JSON_UNQUOTE(JSON_EXTRACT(d.current_steps, '$.*'))
            ORDER BY documents_count DESC
        ";
        
        $stateWorkload = $conn->executeQuery($sql)->fetchAllAssociative();
        
        $totalWorkload = array_sum(array_column($stateWorkload, 'documents_count'));
        
        return [
            'total_active_documents' => $totalWorkload,
            'workload_distribution' => $stateWorkload,
            'bottleneck_states' => array_slice($stateWorkload, 0, 3),
            'utilization_balance' => $this->calculateUtilizationBalance($stateWorkload)
        ];
    }

    /**
     * Calcule une tendance simple
     */
    private function calculateSimpleTrend(array $values): string
    {
        if (count($values) < 2) return 'stable';
        
        $recent = array_slice($values, 0, 3);
        $older = array_slice($values, -3);
        
        $recentAvg = array_sum($recent) / count($recent);
        $olderAvg = array_sum($older) / count($older);
        
        $change = (($recentAvg - $olderAvg) / $olderAvg) * 100;
        
        return $change > 10 ? 'increasing' : ($change < -10 ? 'decreasing' : 'stable');
    }

    /**
     * Évalue l'efficacité
     */
    private function rateEfficiency(float $value): string
    {
        if ($value >= 80) return 'Excellent';
        if ($value >= 60) return 'Bon';
        if ($value >= 40) return 'Moyen';
        return 'Faible';
    }

    /**
     * Calcule la performance par rapport à la cible
     */
    private function calculatePerformanceVsTarget(float $actual, float $target): array
    {
        $variance = (($actual - $target) / $target) * 100;
        
        return [
            'variance_percentage' => round($variance, 1),
            'status' => $variance <= 0 ? 'above_target' : ($variance <= 20 ? 'near_target' : 'below_target')
        ];
    }

    /**
     * Calcule l'équilibre d'utilisation
     */
    private function calculateUtilizationBalance(array $stateWorkload): string
    {
        if (count($stateWorkload) < 2) return 'insufficient_data';
        
        $values = array_column($stateWorkload, 'documents_count');
        $max = max($values);
        $min = min($values);
        
        $ratio = $min > 0 ? $max / $min : $max;
        
        return $ratio < 2 ? 'balanced' : ($ratio < 4 ? 'moderate_imbalance' : 'high_imbalance');
    }

    /**
     * Génère un rapport de performance complet
     */
    public function generatePerformanceReport(): array
    {
        $metrics = $this->calculateAdvancedMetrics();
        
        return [
            'metrics' => $metrics,
            'overall_score' => $this->calculateOverallScore($metrics),
            'key_insights' => $this->generateKeyInsights($metrics),
            'action_items' => $this->generateActionItems($metrics),
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * Calcule un score global de performance
     */
    private function calculateOverallScore(array $metrics): array
    {
        $scores = [
            'throughput' => $this->scoreThroughput($metrics['throughput']),
            'efficiency' => $this->scoreEfficiency($metrics['efficiency']),
            'quality' => $this->scoreQuality($metrics['quality_metrics']),
            'predictability' => $this->scorePredictability($metrics['predictability'])
        ];
        
        $overallScore = array_sum($scores) / count($scores);
        
        return [
            'overall' => round($overallScore, 1),
            'breakdown' => $scores,
            'rating' => $overallScore >= 80 ? 'Excellent' : ($overallScore >= 60 ? 'Bon' : 'À améliorer')
        ];
    }

    private function scoreThroughput(array $throughput): float
    {
        // Score basé sur la tendance et le volume
        $trendScore = match($throughput['trend']) {
            'increasing' => 100,
            'stable' => 75,
            'decreasing' => 50
        };
        
        return $trendScore;
    }

    private function scoreEfficiency(array $efficiency): float
    {
        return min(100, $efficiency['percentage']);
    }

    private function scoreQuality(array $quality): float
    {
        return $quality['first_pass_yield'];
    }

    private function scorePredictability(array $predictability): float
    {
        return match($predictability['predictability_rating']) {
            'Élevée' => 100,
            'Moyenne' => 70,
            'Faible' => 40
        };
    }

    /**
     * Génère des insights clés
     */
    private function generateKeyInsights(array $metrics): array
    {
        $insights = [];
        
        if ($metrics['efficiency']['percentage'] < 60) {
            $insights[] = "Efficacité faible ({$metrics['efficiency']['percentage']}%) - Beaucoup de temps d'attente";
        }
        
        if ($metrics['quality_metrics']['return_rate'] > 15) {
            $insights[] = "Taux de retour élevé ({$metrics['quality_metrics']['return_rate']}%) - Problèmes de qualité";
        }
        
        if ($metrics['predictability']['predictability_rating'] === 'Faible') {
            $insights[] = "Faible prévisibilité - Difficile de planifier les délais";
        }
        
        return $insights;
    }

    /**
     * Génère des actions recommandées
     */
    private function generateActionItems(array $metrics): array
    {
        $actions = [];
        
        if ($metrics['efficiency']['percentage'] < 70) {
            $actions[] = [
                'priority' => 'high',
                'action' => 'Réduire les temps d\'attente entre les états',
                'expected_impact' => 'Amélioration de l\'efficacité de 15-25%'
            ];
        }
        
        if ($metrics['resource_utilization']['utilization_balance'] === 'high_imbalance') {
            $actions[] = [
                'priority' => 'medium',
                'action' => 'Rééquilibrer la charge de travail entre les états',
                'expected_impact' => 'Réduction des goulots d\'étranglement'
            ];
        }
        
        return $actions;
    }
}
