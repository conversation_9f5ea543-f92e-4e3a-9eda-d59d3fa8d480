<?php

namespace App\Service;

use App\Entity\Document;
use App\Repository\DocumentRepository;
use App\Utils\DocumentConstants;
use Doctrine\ORM\EntityManagerInterface;

class DataAnalysisService
{

    private EntityManagerInterface $entityManager;
    private DocumentRepository $documentRepository;

    public function __construct(
        EntityManagerInterface $entityManager,
        DocumentRepository $documentRepository
    ) {
        $this->entityManager = $entityManager;
        $this->documentRepository = $documentRepository;
    }

    /**
     * Analyse les tendances des temps de traitement - Version optimisée
     */
    public function analyzeProcessingTimeTrends(string $period = 'month', int $limit = 6, ?string $docType = null): array
    {
        // Utiliser la méthode optimisée du repository
        return $this->documentRepository->getProcessingTimeTrendsOptimized($period, $limit, $docType);
    }

    /**
     * Récupère les détails d'une période spécifique
     */
    public function getPeriodDetails(string $periodKey, string $period = 'month'): array
    {
        $now = new \DateTime();

        // Définir le format selon la période
        switch ($period) {
            case 'week':
                $format = 'W/Y';
                break;
            case 'month':
                $format = 'm/Y';
                break;
            case 'quarter':
                $format = 'Q/Y';
                break;
            case 'year':
                $format = 'Y';
                break;
            default:
                $format = 'm/Y';
        }

        // Calculer les dates de début et fin de la période
        $startDate = null;
        $endDate = null;

        if ($period === 'month') {
            $parts = explode('/', $periodKey);
            if (count($parts) === 2) {
                $month = (int)$parts[0];
                $year = (int)$parts[1];
                $startDate = new \DateTime("$year-$month-01");
                $endDate = clone $startDate;
                $endDate->modify('last day of this month');
            }
        } elseif ($period === 'year') {
            $year = (int)$periodKey;
            $startDate = new \DateTime("$year-01-01");
            $endDate = new \DateTime("$year-12-31");
        } elseif ($period === 'week') {
            $parts = explode('/', $periodKey);
            if (count($parts) === 2) {
                $week = (int)$parts[0];
                $year = (int)$parts[1];
                $startDate = new \DateTime();
                $startDate->setISODate($year, $week);
                $endDate = clone $startDate;
                $endDate->modify('+6 days');
            }
        } elseif ($period === 'quarter') {
            // Le format peut être "Q1/2024" ou "1/2024" pour les trimestres
            $parts = explode('/', $periodKey);
            if (count($parts) === 2) {
                $quarterStr = $parts[0];
                $year = (int)$parts[1];

                // Extraire le numéro du trimestre (enlever le "Q" ou "T" s'il existe)
                $quarter = (int)preg_replace('/[^0-9]/', '', $quarterStr);

                // Valider le trimestre (1-4)
                if ($quarter >= 1 && $quarter <= 4) {
                    $startMonth = ($quarter - 1) * 3 + 1;
                    $endMonth = $quarter * 3;
                    $startDate = new \DateTime("$year-$startMonth-01");
                    $endDate = new \DateTime("$year-$endMonth-01");
                    $endDate->modify('last day of this month');
                }
            }
        }

        if (!$startDate || !$endDate) {
            return [
                'period' => $periodKey,
                'documents' => [],
                'total_documents' => 0,
                'total_processing_time' => 0,
                'avg_processing_time' => 0,
                'by_place' => [],
                'by_doc_type' => []
            ];
        }

        // Récupérer tous les documents terminés dans cette période
        $documents = $this->documentRepository->findAll();
        $periodDocuments = [];
        $totalTime = 0;
        $byPlace = [];
        $byDocType = [];

        foreach ($documents as $document) {
            // Pour l'analyse, ne considérer que les documents terminés
            if (!$this->isDocumentCompleted($document)) {
                continue;
            }

            $timestamps = $document->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            // Calculer le temps de traitement basé sur les visas BE_0 et Costing
            $firstDate = $document->getVisaDate('visa_BE_0');
            $lastDate = $document->getVisaDate('visa_Costing');

            // Convertir en DateTime si nécessaire
            if ($firstDate instanceof \DateTimeImmutable) {
                $firstDate = \DateTime::createFromImmutable($firstDate);
            }
            if ($lastDate instanceof \DateTimeImmutable) {
                $lastDate = \DateTime::createFromImmutable($lastDate);
            }

            // Vérifier si le document appartient à cette période
            if ($firstDate && $firstDate >= $startDate && $firstDate <= $endDate) {
                if ($lastDate) {
                    $diff = $lastDate->diff($firstDate);
                    $processingTime = $diff->days;
                    $totalTime += $processingTime;

                    // Analyser par place (états actuels)
                    $currentSteps = $document->getCurrentSteps();
                    if ($currentSteps) {
                        foreach ($currentSteps as $state => $value) {
                            if (!isset($byPlace[$state])) {
                                $byPlace[$state] = [
                                    'count' => 0,
                                    'total_time' => 0,
                                    'avg_time' => 0
                                ];
                            }
                            $byPlace[$state]['count']++;
                            $byPlace[$state]['total_time'] += $processingTime;
                        }
                    }

                    // Analyser par type de document
                    $docType = $document->getDocType() ?: 'N/A';
                    if (!isset($byDocType[$docType])) {
                        $byDocType[$docType] = [
                            'count' => 0,
                            'total_time' => 0,
                            'avg_time' => 0
                        ];
                    }
                    $byDocType[$docType]['count']++;
                    $byDocType[$docType]['total_time'] += $processingTime;

                    $periodDocuments[] = [
                        'id' => $document->getId(),
                        'reference' => $document->getReference(),
                        'doc_type' => $docType,
                        'processing_time' => $processingTime,
                        'first_date' => $firstDate->format('Y-m-d'),
                        'last_date' => $lastDate->format('Y-m-d'),
                        'current_steps' => array_keys($currentSteps ?: [])
                    ];
                }
            }
        }

        // Calculer les moyennes
        foreach ($byPlace as $place => &$data) {
            if ($data['count'] > 0) {
                $data['avg_time'] = round($data['total_time'] / $data['count'], 1);
            }
        }

        foreach ($byDocType as $type => &$data) {
            if ($data['count'] > 0) {
                $data['avg_time'] = round($data['total_time'] / $data['count'], 1);
            }
        }

        return [
            'period' => $periodKey,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
            'documents' => $periodDocuments,
            'total_documents' => count($periodDocuments),
            'total_processing_time' => $totalTime,
            'avg_processing_time' => count($periodDocuments) > 0 ? round($totalTime / count($periodDocuments), 1) : 0,
            'by_place' => $byPlace,
            'by_doc_type' => $byDocType
        ];
    }

    /**
     * Prédit le temps de traitement pour un document
     */
    public function predictProcessingTime(Document $document): array
    {
        $docType = $document->getDocType();
        $procType = $document->getProcType();
        $materialType = $document->getMaterialType();

        // Récupérer des documents similaires
        $similarDocuments = $this->documentRepository->findSimilarDocuments($docType, $procType, $materialType);

        $totalTime = 0;
        $count = 0;
        $minTime = PHP_INT_MAX;
        $maxTime = 0;

        foreach ($similarDocuments as $similarDoc) {
            // Pour la prédiction, ne considérer que les documents terminés
            if (!$this->isDocumentCompleted($similarDoc)) {
                continue;
            }

            $timestamps = $similarDoc->getRawStateTimestamps();
            if (!$timestamps) {
                continue;
            }

            // Calculer le temps de traitement basé sur les visas BE_0 et Costing
            $firstDate = $similarDoc->getVisaDate('visa_BE_0');
            $lastDate = $similarDoc->getVisaDate('visa_Costing');

            // Convertir en DateTime si nécessaire
            if ($firstDate instanceof \DateTimeImmutable) {
                $firstDate = \DateTime::createFromImmutable($firstDate);
            }
            if ($lastDate instanceof \DateTimeImmutable) {
                $lastDate = \DateTime::createFromImmutable($lastDate);
            }

            if ($firstDate && $lastDate) {
                $diff = $lastDate->diff($firstDate);
                $processingTime = $diff->days;
                $totalTime += $processingTime;
                $count++;

                $minTime = min($minTime, $processingTime);
                $maxTime = max($maxTime, $processingTime);
            }
        }

        $avgTime = $count > 0 ? round($totalTime / $count, 1) : null;

        return [
            'avg_time' => $avgTime,
            'min_time' => $minTime < PHP_INT_MAX ? $minTime : null,
            'max_time' => $maxTime > 0 ? $maxTime : null,
            'sample_size' => $count,
        ];
    }

    /**
     * Identifie les documents à risque (stagnants) - Version optimisée
     */
    public function identifyRiskyDocuments(int $thresholdDays = 7): array
    {
        // Utiliser la méthode optimisée du repository
        return $this->documentRepository->getRiskyDocumentsOptimized($thresholdDays);
    }

    /**
     * Analyse améliorée des temps de traitement avec méthode de calcul corrigée
     */
    public function getImprovedProcessingTimeAnalysis(): array
    {
        // Utiliser la nouvelle méthode de calcul complet
        $completeStats = $this->documentRepository->getCompleteProcessingTimeStats();

        // Analyser les tendances avec les données corrigées
        $analysis = [
            'doc_type_stats' => $completeStats,
            'overall_stats' => $this->calculateOverallStats($completeStats),
            'recommendations' => $this->generateRecommendations($completeStats)
        ];

        return $analysis;
    }

    /**
     * Calcule les statistiques globales
     */
    private function calculateOverallStats(array $docTypeStats): array
    {
        $totalDocs = 0;
        $totalTime = 0;
        $allTimes = [];

        foreach ($docTypeStats as $stats) {
            $totalDocs += $stats['count'];
            $totalTime += $stats['total_time'];

            // Reconstituer les temps individuels pour les calculs globaux
            if (isset($stats['avg_time']) && $stats['count'] > 0) {
                for ($i = 0; $i < $stats['count']; $i++) {
                    $allTimes[] = $stats['avg_time'];
                }
            }
        }

        $overallAvg = $totalDocs > 0 ? round($totalTime / $totalDocs, 1) : 0;

        return [
            'total_documents' => $totalDocs,
            'overall_avg_time' => $overallAvg,
            'sample_quality' => $totalDocs > 100 ? 'good' : ($totalDocs > 50 ? 'fair' : 'poor')
        ];
    }

    /**
     * Génère des recommandations basées sur l'analyse
     */
    private function generateRecommendations(array $docTypeStats): array
    {
        $recommendations = [];

        foreach ($docTypeStats as $docType => $stats) {
            if ($stats['count'] < 10) {
                $recommendations[] = [
                    'type' => 'warning',
                    'message' => "Échantillon trop petit pour {$docType} ({$stats['count']} documents)",
                    'suggestion' => 'Augmenter la période d\'analyse ou réviser les critères de filtrage'
                ];
            }

            if (isset($stats['avg_time']) && $stats['avg_time'] < 5) {
                $recommendations[] = [
                    'type' => 'error',
                    'message' => "Temps moyen suspicieusement court pour {$docType} ({$stats['avg_time']} jours)",
                    'suggestion' => 'Vérifier la méthode de calcul et les données sources'
                ];
            }

            if (isset($stats['max_time'], $stats['min_time']) && $stats['max_time'] > $stats['min_time'] * 10) {
                $recommendations[] = [
                    'type' => 'info',
                    'message' => "Grande variabilité détectée pour {$docType} (min: {$stats['min_time']}, max: {$stats['max_time']})",
                    'suggestion' => 'Analyser les causes de cette variabilité'
                ];
            }
        }

        return $recommendations;
    }



    /**
     * Vérifie si un document est terminé pour un état donné
     * Un état est terminé s'il a le visa correspondant ou si c'est un état panier
     */
    private function isDocumentCompletedForState(Document $document, string $state): bool
    {
        // États considérés comme "paniers" - documents dans ces états sont considérés comme terminés
        $panierStates = DocumentConstants::PANIER_STATES;

        // Si le document est dans un état panier, il est considéré comme terminé
        if (in_array($state, $panierStates)) {
            return true;
        }

        // Vérifier si le document a le visa correspondant à l'état
        $visaName = 'visa_' . $state;

        // Cas spécial pour les états logistiques
        if ($state === 'Qual_Logistique' || $state === 'Logistique') {
            // Pour les états logistiques, vérifier si les deux visas sont présents
            return $document->hasVisa('visa_Qual_Logistique') && $document->hasVisa('visa_Logistique');
        }

        return $document->hasVisa($visaName);
    }

    /**
     * Vérifie si un document est globalement terminé
     * Utilisé pour la partie analyse
     *
     * Un document est terminé s'il a le visa Costing (fin du workflow).
     * Pour les analyses de temps, on ne considère que les documents avec visa_BE_0 ET visa_Costing.
     */
    public function isDocumentCompleted(Document $document): bool
    {
        // Un document est terminé s'il a le visa Costing
        $hasCosting = $document->hasVisa('visa_Costing');
        $hasBE0 = $document->hasVisa('visa_BE_0');

        // Pour les analyses, on ne considère que les documents qui ont un cycle complet
        return $hasCosting && $hasBE0;
    }
}
