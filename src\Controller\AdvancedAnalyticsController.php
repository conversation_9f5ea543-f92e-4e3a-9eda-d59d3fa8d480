<?php

namespace App\Controller;

use App\Repository\DocumentRepository;
use App\Service\AdvancedPredictionService;
use App\Service\AdvancedMetricsService;
use App\Service\DataAnalysisService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/analytics')]
class AdvancedAnalyticsController extends AbstractController
{
    public function __construct(
        private DocumentRepository $documentRepository,
        private AdvancedPredictionService $predictionService,
        private AdvancedMetricsService $metricsService,
        private DataAnalysisService $dataAnalysisService
    ) {}

    #[Route('/dashboard', name: 'app_analytics_dashboard', methods: ['GET'])]
    public function dashboard(): Response
    {
        // Récupérer les métriques principales
        $metrics = $this->metricsService->calculateAdvancedMetrics();
        $performanceReport = $this->metricsService->generatePerformanceReport();
        $trends = $this->dataAnalysisService->analyzePerformanceTrends(6);
        
        return $this->render('analytics/dashboard.html.twig', [
            'metrics' => $metrics,
            'performance_report' => $performanceReport,
            'trends' => $trends
        ]);
    }

    #[Route('/predictions', name: 'app_analytics_predictions', methods: ['GET'])]
    public function predictions(): Response
    {
        // Analyser quelques documents pour les prédictions
        $sampleDocuments = $this->documentRepository->findBy([], null, 10);
        $predictions = [];
        
        foreach ($sampleDocuments as $document) {
            $prediction = $this->predictionService->predictAdvancedProcessingTime($document);
            $predictions[] = [
                'document' => $document,
                'prediction' => $prediction
            ];
        }
        
        // Prédictions de charge de travail
        $workloadPredictions = $this->predictionService->predictWorkloadTrends(30);
        
        return $this->render('analytics/predictions.html.twig', [
            'document_predictions' => $predictions,
            'workload_predictions' => $workloadPredictions,
            'accuracy_analysis' => $this->predictionService->analyzePredictionAccuracy()
        ]);
    }

    #[Route('/api/predict/{id}', name: 'app_analytics_api_predict', methods: ['GET'])]
    public function predictDocument(int $id): JsonResponse
    {
        $document = $this->documentRepository->find($id);
        
        if (!$document) {
            return new JsonResponse(['error' => 'Document non trouvé'], 404);
        }
        
        $prediction = $this->predictionService->predictAdvancedProcessingTime($document);
        
        return new JsonResponse([
            'document_id' => $id,
            'reference' => $document->getReference(),
            'prediction' => $prediction,
            'generated_at' => date('Y-m-d H:i:s')
        ]);
    }

    #[Route('/api/metrics', name: 'app_analytics_api_metrics', methods: ['GET'])]
    public function getMetrics(): JsonResponse
    {
        $metrics = $this->metricsService->calculateAdvancedMetrics();
        
        return new JsonResponse([
            'metrics' => $metrics,
            'generated_at' => date('Y-m-d H:i:s')
        ]);
    }

    #[Route('/api/performance-report', name: 'app_analytics_api_performance_report', methods: ['GET'])]
    public function getPerformanceReport(): JsonResponse
    {
        $report = $this->metricsService->generatePerformanceReport();
        
        return new JsonResponse($report);
    }

    #[Route('/trends', name: 'app_analytics_trends', methods: ['GET'])]
    public function trends(Request $request): Response
    {
        $months = $request->query->getInt('months', 12);
        $docType = $request->query->get('doc_type');
        
        // Analyser les tendances
        $performanceTrends = $this->dataAnalysisService->analyzePerformanceTrends($months);
        $processingTrends = $this->dataAnalysisService->analyzeProcessingTimeTrends('month', $months, $docType);
        
        return $this->render('analytics/trends.html.twig', [
            'performance_trends' => $performanceTrends,
            'processing_trends' => $processingTrends,
            'selected_months' => $months,
            'selected_doc_type' => $docType
        ]);
    }

    #[Route('/optimization', name: 'app_analytics_optimization', methods: ['GET'])]
    public function optimization(): Response
    {
        // Analyser les opportunités d'optimisation
        $metrics = $this->metricsService->calculateAdvancedMetrics();
        $bottlenecks = $this->documentRepository->getStateBottleneckAnalysis();
        
        // Générer des recommandations d'optimisation
        $optimizations = $this->generateOptimizationRecommendations($metrics, $bottlenecks);
        
        return $this->render('analytics/optimization.html.twig', [
            'metrics' => $metrics,
            'bottlenecks' => $bottlenecks,
            'optimizations' => $optimizations
        ]);
    }

    #[Route('/api/workload-forecast', name: 'app_analytics_api_workload_forecast', methods: ['GET'])]
    public function getWorkloadForecast(Request $request): JsonResponse
    {
        $days = $request->query->getInt('days', 30);
        $forecast = $this->predictionService->predictWorkloadTrends($days);
        
        return new JsonResponse([
            'forecast' => $forecast,
            'period_days' => $days,
            'generated_at' => date('Y-m-d H:i:s')
        ]);
    }

    #[Route('/export/metrics', name: 'app_analytics_export_metrics', methods: ['GET'])]
    public function exportMetrics(): Response
    {
        $report = $this->metricsService->generatePerformanceReport();
        
        // Créer un CSV
        $csv = $this->generateMetricsCSV($report);
        
        $response = new Response($csv);
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="metrics_' . date('Y-m-d') . '.csv"');
        
        return $response;
    }

    /**
     * Génère des recommandations d'optimisation
     */
    private function generateOptimizationRecommendations(array $metrics, array $bottlenecks): array
    {
        $recommendations = [];
        
        // Analyser l'efficacité
        if ($metrics['efficiency']['percentage'] < 70) {
            $recommendations[] = [
                'category' => 'Efficacité',
                'priority' => 'high',
                'title' => 'Améliorer l\'efficacité du processus',
                'description' => "L'efficacité actuelle est de {$metrics['efficiency']['percentage']}%. Il y a {$metrics['efficiency']['waste_time']} jours de temps d'attente en moyenne.",
                'actions' => [
                    'Identifier et éliminer les temps d\'attente',
                    'Paralléliser les tâches non-dépendantes',
                    'Automatiser les validations simples'
                ],
                'expected_impact' => 'Réduction de 20-30% du temps total'
            ];
        }
        
        // Analyser les goulots
        $topBottlenecks = array_slice($bottlenecks, 0, 3, true);
        foreach ($topBottlenecks as $state => $stats) {
            if ($stats['avg_time'] > 10) {
                $recommendations[] = [
                    'category' => 'Goulots d\'étranglement',
                    'priority' => 'medium',
                    'title' => "Optimiser l'état {$state}",
                    'description' => "L'état {$state} prend en moyenne {$stats['avg_time']} jours avec {$stats['document_count']} documents.",
                    'actions' => [
                        'Analyser les causes de lenteur',
                        'Augmenter les ressources si nécessaire',
                        'Revoir les procédures'
                    ],
                    'expected_impact' => 'Réduction de 15-25% du temps dans cet état'
                ];
            }
        }
        
        // Analyser la qualité
        if ($metrics['quality_metrics']['return_rate'] > 10) {
            $recommendations[] = [
                'category' => 'Qualité',
                'priority' => 'high',
                'title' => 'Réduire le taux de retour',
                'description' => "Le taux de retour est de {$metrics['quality_metrics']['return_rate']}%, ce qui génère du travail supplémentaire.",
                'actions' => [
                    'Améliorer les contrôles en amont',
                    'Former les équipes sur les standards',
                    'Mettre en place des check-lists'
                ],
                'expected_impact' => 'Réduction de 50% des retours'
            ];
        }
        
        // Analyser la prévisibilité
        if ($metrics['predictability']['predictability_rating'] === 'Faible') {
            $recommendations[] = [
                'category' => 'Prévisibilité',
                'priority' => 'medium',
                'title' => 'Améliorer la prévisibilité',
                'description' => "La variabilité des délais est élevée ({$metrics['predictability']['coefficient_of_variation']}%), rendant la planification difficile.",
                'actions' => [
                    'Standardiser les processus',
                    'Identifier les causes de variabilité',
                    'Mettre en place des SLA par état'
                ],
                'expected_impact' => 'Amélioration de 40% de la prévisibilité'
            ];
        }
        
        return $recommendations;
    }

    /**
     * Génère un CSV des métriques
     */
    private function generateMetricsCSV(array $report): string
    {
        $csv = "Métrique,Valeur,Unité,Statut\n";
        
        $metrics = $report['metrics'];
        
        $csv .= "Débit moyen,{$metrics['throughput']['average_per_month']},docs/mois,{$metrics['throughput']['trend']}\n";
        $csv .= "Temps de cycle,{$metrics['cycle_time']['total_active_time']},jours,{$metrics['cycle_time']['efficiency_rating']}\n";
        $csv .= "Lead time,{$metrics['lead_time']['average']},jours,{$metrics['lead_time']['performance']['status']}\n";
        $csv .= "Efficacité,{$metrics['efficiency']['percentage']},%,{$metrics['efficiency']['rating']}\n";
        $csv .= "Taux de retour,{$metrics['quality_metrics']['return_rate']},%,{$metrics['quality_metrics']['quality_rating']}\n";
        $csv .= "Prévisibilité,{$metrics['predictability']['coefficient_of_variation']},%,{$metrics['predictability']['predictability_rating']}\n";
        
        $csv .= "\nScore global,{$report['overall_score']['overall']}/100,,{$report['overall_score']['rating']}\n";
        
        return $csv;
    }
}
