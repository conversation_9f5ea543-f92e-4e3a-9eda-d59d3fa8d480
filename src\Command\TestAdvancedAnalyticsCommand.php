<?php

namespace App\Command;

use App\Repository\DocumentRepository;
use App\Service\AdvancedPredictionService;
use App\Service\AdvancedMetricsService;
use App\Service\DataAnalysisService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-advanced-analytics',
    description: 'Test des analyses avancées'
)]
class TestAdvancedAnalyticsCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private DocumentRepository $documentRepository,
        private AdvancedPredictionService $advancedPredictionService,
        private AdvancedMetricsService $advancedMetricsService,
        private DataAnalysisService $dataAnalysisService
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('TEST DES ANALYSES AVANCÉES');

        // 1. Tester le service de métriques avancées
        $io->section('1. Test AdvancedMetricsService');
        try {
            $metrics = $this->advancedMetricsService->calculateAdvancedMetrics();
            
            $io->success('Métriques calculées avec succès');
            $io->listing([
                "Débit moyen: {$metrics['throughput']['average_per_month']} docs/mois",
                "Efficacité: {$metrics['efficiency']['percentage']}%",
                "Lead time: {$metrics['lead_time']['average']} jours",
                "Qualité (1er passage): {$metrics['quality_metrics']['first_pass_yield']}%",
                "Prévisibilité: {$metrics['predictability']['predictability_rating']}"
            ]);
            
        } catch (\Exception $e) {
            $io->error('Erreur: ' . $e->getMessage());
        }

        // 2. Tester le rapport de performance
        $io->section('2. Test Rapport de Performance');
        try {
            $report = $this->advancedMetricsService->generatePerformanceReport();
            
            $io->success('Rapport généré avec succès');
            $io->listing([
                "Score global: {$report['overall_score']['overall']}/100 ({$report['overall_score']['rating']})",
                "Insights clés: " . count($report['key_insights']),
                "Actions recommandées: " . count($report['action_items'])
            ]);
            
            if (count($report['key_insights']) > 0) {
                $io->text('Premiers insights:');
                foreach (array_slice($report['key_insights'], 0, 3) as $insight) {
                    $io->text("  • {$insight}");
                }
            }
            
        } catch (\Exception $e) {
            $io->error('Erreur: ' . $e->getMessage());
        }

        // 3. Tester les prédictions avancées
        $io->section('3. Test Prédictions Avancées');
        try {
            $sampleDocuments = $this->documentRepository->findBy([], null, 3);
            
            if (count($sampleDocuments) > 0) {
                $io->success('Test sur ' . count($sampleDocuments) . ' documents:');
                
                foreach ($sampleDocuments as $document) {
                    $prediction = $this->advancedPredictionService->predictAdvancedProcessingTime($document);
                    
                    $io->text("Document {$document->getReference()} ({$document->getDocType()}):");
                    $io->listing([
                        "Prédiction: {$prediction['predicted_days']} jours",
                        "Confiance: {$prediction['confidence_level']}",
                        "Facteurs: DocType({$prediction['breakdown']['doc_type_factor']}), État({$prediction['breakdown']['state_factor']}), Saison({$prediction['breakdown']['seasonal_factor']})"
                    ]);
                    
                    if (count($prediction['recommendations']) > 0) {
                        $io->text("Recommandations: " . implode(', ', array_slice($prediction['recommendations'], 0, 2)));
                    }
                }
            } else {
                $io->warning('Aucun document trouvé pour les tests');
            }
            
        } catch (\Exception $e) {
            $io->error('Erreur: ' . $e->getMessage());
        }

        // 4. Tester l'analyse des tendances de performance
        $io->section('4. Test Analyse des Tendances');
        try {
            $trends = $this->dataAnalysisService->analyzePerformanceTrends(6);
            
            $io->success('Analyse des tendances sur 6 mois:');
            $io->listing([
                "Données mensuelles: " . count($trends['monthly_data']) . " mois",
                "Direction: {$trends['trend_analysis']['direction']}",
                "Changement: {$trends['trend_analysis']['change_percentage']}%",
                "Confiance: {$trends['trend_analysis']['confidence']}"
            ]);
            
            if (count($trends['performance_insights']) > 0) {
                $io->text('Insights de performance:');
                foreach ($trends['performance_insights'] as $insight) {
                    $io->text("  • {$insight}");
                }
            }
            
        } catch (\Exception $e) {
            $io->error('Erreur: ' . $e->getMessage());
        }

        // 5. Tester les prédictions de charge de travail
        $io->section('5. Test Prédictions de Charge de Travail');
        try {
            $workloadForecast = $this->advancedPredictionService->predictWorkloadTrends(7);
            
            $io->success('Prédictions sur 7 jours:');
            foreach (array_slice($workloadForecast, 0, 3) as $forecast) {
                $io->text("{$forecast['date']}: {$forecast['predicted_new_docs']} nouveaux docs (charge: {$forecast['predicted_workload']})");
            }
            
        } catch (\Exception $e) {
            $io->error('Erreur: ' . $e->getMessage());
        }

        // 6. Comparer avec l'ancien système
        $io->section('6. Comparaison avec l\'ancien système');
        try {
            // Ancien système (méthode simple)
            $oldAverages = $this->documentRepository->getAverageTimeByState();
            $oldAvgTime = count($oldAverages) > 0 ? array_sum($oldAverages) / count($oldAverages) : 0;
            
            // Nouveau système
            $newMetrics = $this->advancedMetricsService->calculateAdvancedMetrics();
            $newAvgTime = $newMetrics['lead_time']['average'];
            
            $io->text('Temps moyen:');
            $io->listing([
                "Ancien système: " . round($oldAvgTime, 1) . " jours (basé sur " . count($oldAverages) . " états)",
                "Nouveau système: {$newAvgTime} jours (lead time complet)",
                "Différence: " . round($newAvgTime - $oldAvgTime, 1) . " jours"
            ]);
            
            $io->text('Avantages du nouveau système:');
            $io->listing([
                'Prédictions multi-facteurs',
                'Métriques de qualité',
                'Analyse de tendances',
                'Recommandations automatiques',
                'Score de performance global'
            ]);
            
        } catch (\Exception $e) {
            $io->error('Erreur: ' . $e->getMessage());
        }

        // 7. Recommandations d'utilisation
        $io->section('7. Recommandations d\'utilisation');
        $io->listing([
            'Dashboard principal: /analytics/dashboard',
            'Prédictions: /analytics/predictions',
            'Tendances: /analytics/trends',
            'Optimisation: /analytics/optimization',
            'Export: /analytics/export/metrics'
        ]);
        
        $io->text('API disponibles:');
        $io->listing([
            'GET /analytics/api/metrics (métriques en temps réel)',
            'GET /analytics/api/predict/{id} (prédiction pour un document)',
            'GET /analytics/api/performance-report (rapport complet)',
            'GET /analytics/api/workload-forecast (prévisions de charge)'
        ]);

        // 8. Vérification de la qualité des données
        $io->section('8. Qualité des données');
        $conn = $this->entityManager->getConnection();

        $totalDocs = $this->documentRepository->count([]);
        $docsWithTimestamps = $conn->executeQuery("
            SELECT COUNT(*) FROM document 
            WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''
        ")->fetchOne();

        $docsWithCompleteData = $conn->executeQuery("
            SELECT COUNT(*) FROM document d
            WHERE JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter') IS NOT NULL
            AND (
                JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit') IS NOT NULL
                OR JSON_EXTRACT(d.state_timestamps, '$.Quality[0].exit') IS NOT NULL
            )
        ")->fetchOne();

        $timestampCoverage = round(($docsWithTimestamps / $totalDocs) * 100, 1);
        $completeCoverage = round(($docsWithCompleteData / $totalDocs) * 100, 1);

        $io->listing([
            "Documents totaux: {$totalDocs}",
            "Avec timestamps: {$docsWithTimestamps} ({$timestampCoverage}%)",
            "Avec données complètes: {$docsWithCompleteData} ({$completeCoverage}%)"
        ]);

        if ($completeCoverage < 20) {
            $io->warning('ATTENTION: Couverture faible des données complètes');
            $io->note('RECOMMANDATION: Améliorer l\'enregistrement des timestamps de sortie');
        } else {
            $io->success('Couverture suffisante pour des analyses fiables');
        }

        $io->success('Test terminé avec succès !');
        return Command::SUCCESS;
    }
}
