<?php

require_once 'vendor/autoload.php';

use App\Kernel;
use Symfony\Component\Dotenv\Dotenv;

// Charger les variables d'environnement
$dotenv = new Dotenv();
$dotenv->load('.env');

// Créer le kernel Symfony
$kernel = new Kernel('dev', true);
$kernel->boot();
$container = $kernel->getContainer();

// Récupérer les services nécessaires
$entityManager = $container->get('doctrine.orm.entity_manager');
$documentRepository = $entityManager->getRepository('App\Entity\Document');
$advancedPredictionService = $container->get('App\Service\AdvancedPredictionService');
$advancedMetricsService = $container->get('App\Service\AdvancedMetricsService');
$dataAnalysisService = $container->get('App\Service\DataAnalysisService');

echo "=== TEST DES ANALYSES AVANCÉES ===\n\n";

// 1. Tester le service de métriques avancées
echo "1. TEST AdvancedMetricsService:\n";
try {
    $metrics = $advancedMetricsService->calculateAdvancedMetrics();
    
    echo "   ✅ Métriques calculées avec succès\n";
    echo "   - Débit moyen: {$metrics['throughput']['average_per_month']} docs/mois\n";
    echo "   - Efficacité: {$metrics['efficiency']['percentage']}%\n";
    echo "   - Lead time: {$metrics['lead_time']['average']} jours\n";
    echo "   - Qualité (1er passage): {$metrics['quality_metrics']['first_pass_yield']}%\n";
    echo "   - Prévisibilité: {$metrics['predictability']['predictability_rating']}\n";
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

// 2. Tester le rapport de performance
echo "\n2. TEST Rapport de Performance:\n";
try {
    $report = $advancedMetricsService->generatePerformanceReport();
    
    echo "   ✅ Rapport généré avec succès\n";
    echo "   - Score global: {$report['overall_score']['overall']}/100 ({$report['overall_score']['rating']})\n";
    echo "   - Insights clés: " . count($report['key_insights']) . "\n";
    echo "   - Actions recommandées: " . count($report['action_items']) . "\n";
    
    if (count($report['key_insights']) > 0) {
        echo "   Premiers insights:\n";
        foreach (array_slice($report['key_insights'], 0, 3) as $insight) {
            echo "     * {$insight}\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

// 3. Tester les prédictions avancées
echo "\n3. TEST Prédictions Avancées:\n";
try {
    $sampleDocuments = $documentRepository->findBy([], null, 3);
    
    if (count($sampleDocuments) > 0) {
        echo "   ✅ Test sur " . count($sampleDocuments) . " documents:\n";
        
        foreach ($sampleDocuments as $document) {
            $prediction = $advancedPredictionService->predictAdvancedProcessingTime($document);
            
            echo "   - {$document->getReference()} ({$document->getDocType()}):\n";
            echo "     * Prédiction: {$prediction['predicted_days']} jours\n";
            echo "     * Confiance: {$prediction['confidence_level']}\n";
            echo "     * Facteurs: DocType({$prediction['breakdown']['doc_type_factor']}), ";
            echo "État({$prediction['breakdown']['state_factor']}), ";
            echo "Saison({$prediction['breakdown']['seasonal_factor']})\n";
            
            if (count($prediction['recommendations']) > 0) {
                echo "     * Recommandations: " . implode(', ', array_slice($prediction['recommendations'], 0, 2)) . "\n";
            }
        }
    } else {
        echo "   ⚠️  Aucun document trouvé pour les tests\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

// 4. Tester l'analyse des tendances de performance
echo "\n4. TEST Analyse des Tendances:\n";
try {
    $trends = $dataAnalysisService->analyzePerformanceTrends(6);
    
    echo "   ✅ Analyse des tendances sur 6 mois:\n";
    echo "   - Données mensuelles: " . count($trends['monthly_data']) . " mois\n";
    echo "   - Direction: {$trends['trend_analysis']['direction']}\n";
    echo "   - Changement: {$trends['trend_analysis']['change_percentage']}%\n";
    echo "   - Confiance: {$trends['trend_analysis']['confidence']}\n";
    
    if (count($trends['performance_insights']) > 0) {
        echo "   Insights de performance:\n";
        foreach ($trends['performance_insights'] as $insight) {
            echo "     * {$insight}\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

// 5. Tester les prédictions de charge de travail
echo "\n5. TEST Prédictions de Charge de Travail:\n";
try {
    $workloadForecast = $advancedPredictionService->predictWorkloadTrends(7);
    
    echo "   ✅ Prédictions sur 7 jours:\n";
    foreach (array_slice($workloadForecast, 0, 3) as $forecast) {
        echo "   - {$forecast['date']}: {$forecast['predicted_new_docs']} nouveaux docs (charge: {$forecast['predicted_workload']})\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

// 6. Tester l'analyse de précision des prédictions
echo "\n6. TEST Analyse de Précision:\n";
try {
    $accuracy = $advancedPredictionService->analyzePredictionAccuracy();
    
    echo "   ✅ Analyse de précision:\n";
    echo "   - Taux de précision: {$accuracy['accuracy_rate']}%\n";
    echo "   - Erreur moyenne: {$accuracy['avg_error']} jours\n";
    echo "   - Suggestions d'amélioration: " . count($accuracy['improvement_suggestions']) . "\n";
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

// 7. Comparer avec l'ancien système
echo "\n7. COMPARAISON avec l'ancien système:\n";
try {
    // Ancien système (méthode simple)
    $oldAverages = $documentRepository->getAverageTimeByState();
    $oldAvgTime = count($oldAverages) > 0 ? array_sum($oldAverages) / count($oldAverages) : 0;
    
    // Nouveau système
    $newMetrics = $advancedMetricsService->calculateAdvancedMetrics();
    $newAvgTime = $newMetrics['lead_time']['average'];
    
    echo "   Temps moyen:\n";
    echo "   - Ancien système: " . round($oldAvgTime, 1) . " jours (basé sur " . count($oldAverages) . " états)\n";
    echo "   - Nouveau système: {$newAvgTime} jours (lead time complet)\n";
    echo "   - Différence: " . round($newAvgTime - $oldAvgTime, 1) . " jours\n";
    
    echo "   Avantages du nouveau système:\n";
    echo "   ✅ Prédictions multi-facteurs\n";
    echo "   ✅ Métriques de qualité\n";
    echo "   ✅ Analyse de tendances\n";
    echo "   ✅ Recommandations automatiques\n";
    echo "   ✅ Score de performance global\n";
    
} catch (Exception $e) {
    echo "   ❌ Erreur: " . $e->getMessage() . "\n";
}

// 8. Recommandations d'utilisation
echo "\n8. RECOMMANDATIONS D'UTILISATION:\n";
echo "   📊 Dashboard principal: /analytics/dashboard\n";
echo "   🔮 Prédictions: /analytics/predictions\n";
echo "   📈 Tendances: /analytics/trends\n";
echo "   ⚙️  Optimisation: /analytics/optimization\n";
echo "   📥 Export: /analytics/export/metrics\n";
echo "\n   API disponibles:\n";
echo "   - GET /analytics/api/metrics (métriques en temps réel)\n";
echo "   - GET /analytics/api/predict/{id} (prédiction pour un document)\n";
echo "   - GET /analytics/api/performance-report (rapport complet)\n";
echo "   - GET /analytics/api/workload-forecast (prévisions de charge)\n";

// 9. Vérification de la qualité des données
echo "\n9. QUALITÉ DES DONNÉES:\n";
$conn = $entityManager->getConnection();

$totalDocs = $documentRepository->count([]);
$docsWithTimestamps = $conn->executeQuery("
    SELECT COUNT(*) FROM document 
    WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''
")->fetchOne();

$docsWithCompleteData = $conn->executeQuery("
    SELECT COUNT(*) FROM document d
    WHERE JSON_EXTRACT(d.state_timestamps, '$.BE_0[0].enter') IS NOT NULL
    AND (
        JSON_EXTRACT(d.state_timestamps, '$.Costing[0].exit') IS NOT NULL
        OR JSON_EXTRACT(d.state_timestamps, '$.Quality[0].exit') IS NOT NULL
    )
")->fetchOne();

$timestampCoverage = round(($docsWithTimestamps / $totalDocs) * 100, 1);
$completeCoverage = round(($docsWithCompleteData / $totalDocs) * 100, 1);

echo "   - Documents totaux: {$totalDocs}\n";
echo "   - Avec timestamps: {$docsWithTimestamps} ({$timestampCoverage}%)\n";
echo "   - Avec données complètes: {$docsWithCompleteData} ({$completeCoverage}%)\n";

if ($completeCoverage < 20) {
    echo "   ⚠️  ATTENTION: Couverture faible des données complètes\n";
    echo "   📝 RECOMMANDATION: Améliorer l'enregistrement des timestamps de sortie\n";
} else {
    echo "   ✅ Couverture suffisante pour des analyses fiables\n";
}

echo "\n=== FIN DU TEST ===\n";
