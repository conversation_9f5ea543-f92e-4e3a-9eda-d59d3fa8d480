{% extends 'base.html.twig' %}

{% block title %}Tableau de Bord Analytique{% endblock %}

{% block body %}
<div class="container-fluid mt-4">
    <!-- En-tête -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Tableau de Bord Analytique Avancé
                    </h1>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-0">
                        Analyses avancées des performances et prédictions basées sur les données réelles.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Score Global de Performance -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-trophy me-2 text-warning"></i>
                        Score Global de Performance
                    </h5>
                    <span class="badge bg-{{ performance_report.overall_score.overall >= 80 ? 'success' : (performance_report.overall_score.overall >= 60 ? 'warning' : 'danger') }} fs-6">
                        {{ performance_report.overall_score.overall }}/100
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="progress mb-3" style="height: 25px;">
                                <div class="progress-bar bg-{{ performance_report.overall_score.overall >= 80 ? 'success' : (performance_report.overall_score.overall >= 60 ? 'warning' : 'danger') }}" 
                                     role="progressbar" 
                                     style="width: {{ performance_report.overall_score.overall }}%">
                                    {{ performance_report.overall_score.rating }}
                                </div>
                            </div>
                            <div class="row">
                                {% for category, score in performance_report.overall_score.breakdown %}
                                <div class="col-6 mb-2">
                                    <small class="text-muted">{{ category|title }}</small>
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar" style="width: {{ score }}%"></div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6 class="text-muted">Insights Clés</h6>
                            {% for insight in performance_report.key_insights %}
                            <div class="alert alert-info py-2 mb-2">
                                <small>{{ insight }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Métriques Principales -->
    <div class="row mb-4">
        <!-- Débit -->
        <div class="col-md-3 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-tachometer-alt fa-2x text-primary mb-3"></i>
                    <h4 class="text-primary">{{ metrics.throughput.average_per_month }}</h4>
                    <p class="text-muted mb-1">Documents/mois</p>
                    <small class="badge bg-{{ metrics.throughput.trend == 'increasing' ? 'success' : (metrics.throughput.trend == 'stable' ? 'warning' : 'danger') }}">
                        {{ metrics.throughput.trend == 'increasing' ? 'En hausse' : (metrics.throughput.trend == 'stable' ? 'Stable' : 'En baisse') }}
                    </small>
                </div>
            </div>
        </div>

        <!-- Efficacité -->
        <div class="col-md-3 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-chart-pie fa-2x text-success mb-3"></i>
                    <h4 class="text-success">{{ metrics.efficiency.percentage }}%</h4>
                    <p class="text-muted mb-1">Efficacité</p>
                    <small class="text-muted">{{ metrics.efficiency.rating }}</small>
                </div>
            </div>
        </div>

        <!-- Lead Time -->
        <div class="col-md-3 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-info mb-3"></i>
                    <h4 class="text-info">{{ metrics.lead_time.average }}</h4>
                    <p class="text-muted mb-1">Jours (Lead Time)</p>
                    <small class="badge bg-{{ metrics.lead_time.performance.status == 'above_target' ? 'success' : (metrics.lead_time.performance.status == 'near_target' ? 'warning' : 'danger') }}">
                        {{ metrics.lead_time.performance.variance_percentage }}% vs cible
                    </small>
                </div>
            </div>
        </div>

        <!-- Qualité -->
        <div class="col-md-3 mb-3">
            <div class="card h-100">
                <div class="card-body text-center">
                    <i class="fas fa-award fa-2x text-warning mb-3"></i>
                    <h4 class="text-warning">{{ metrics.quality_metrics.first_pass_yield }}%</h4>
                    <p class="text-muted mb-1">Qualité (1er passage)</p>
                    <small class="text-muted">{{ metrics.quality_metrics.quality_rating }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Graphiques et Analyses -->
    <div class="row mb-4">
        <!-- Tendances de Performance -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Tendances de Performance
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="performanceTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Utilisation des Ressources -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users me-2"></i>
                        Charge de Travail
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Documents actifs</span>
                            <strong>{{ metrics.resource_utilization.total_active_documents }}</strong>
                        </div>
                    </div>
                    
                    <h6 class="text-muted mb-3">États les plus chargés</h6>
                    {% for state in metrics.resource_utilization.bottleneck_states %}
                    <div class="d-flex justify-content-between mb-2">
                        <span class="small">{{ state.current_state }}</span>
                        <span class="badge bg-primary">{{ state.documents_count }}</span>
                    </div>
                    {% endfor %}
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            Équilibre: 
                            <span class="badge bg-{{ metrics.resource_utilization.utilization_balance == 'balanced' ? 'success' : 'warning' }}">
                                {{ metrics.resource_utilization.utilization_balance == 'balanced' ? 'Équilibré' : 'Déséquilibré' }}
                            </span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions Recommandées -->
    {% if performance_report.action_items|length > 0 %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Actions Recommandées
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for action in performance_report.action_items %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-{{ action.priority == 'high' ? 'danger' : 'warning' }}">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">{{ action.action }}</h6>
                                        <span class="badge bg-{{ action.priority == 'high' ? 'danger' : 'warning' }}">
                                            {{ action.priority == 'high' ? 'Priorité haute' : 'Priorité moyenne' }}
                                        </span>
                                    </div>
                                    <p class="card-text small text-muted">{{ action.expected_impact }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Navigation vers d'autres analyses -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-compass me-2"></i>
                        Analyses Détaillées
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2 flex-wrap">
                        <a href="{{ path('app_analytics_predictions') }}" class="btn btn-primary">
                            <i class="fas fa-crystal-ball me-1"></i>
                            Prédictions
                        </a>
                        <a href="{{ path('app_analytics_trends') }}" class="btn btn-info">
                            <i class="fas fa-chart-area me-1"></i>
                            Tendances
                        </a>
                        <a href="{{ path('app_analytics_optimization') }}" class="btn btn-success">
                            <i class="fas fa-cogs me-1"></i>
                            Optimisation
                        </a>
                        <a href="{{ path('app_analytics_export_metrics') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-download me-1"></i>
                            Exporter
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique des tendances de performance
const ctx = document.getElementById('performanceTrendsChart').getContext('2d');
const trendsData = {{ trends.monthly_data|json_encode|raw }};

new Chart(ctx, {
    type: 'line',
    data: {
        labels: trendsData.map(d => d.month),
        datasets: [{
            label: 'Documents traités',
            data: trendsData.map(d => d.documents_started),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            yAxisID: 'y'
        }, {
            label: 'Temps moyen (jours)',
            data: trendsData.map(d => d.avg_processing_time),
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Nombre de documents'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Temps (jours)'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});
</script>
{% endblock %}
